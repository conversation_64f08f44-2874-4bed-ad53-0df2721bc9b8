{"Files": [{"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\aad56d02-85fa-4452-a727-24299a7a7bc5_4FD208543529D44B30E586A9A66719C5.png", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\aad56d02-85fa-4452-a727-24299a7a7bc5_4FD208543529D44B30E586A9A66719C5.png"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.WatermarkCore.props", "PackagePath": "build\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.WatermarkCore.props", "PackagePath": "buildMultiTargeting\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.WatermarkCore.props", "PackagePath": "buildTransitive\\WatermarkCore.props"}], "ElementsToRemove": []}