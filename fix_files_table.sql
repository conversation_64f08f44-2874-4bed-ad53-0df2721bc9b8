-- 修复 Files 表结构
USE WatermarkSys;

-- 删除外键约束检查
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有的 Files 表并重新创建
DROP TABLE IF EXISTS Files;

-- 重新创建 Files 表，包含所有必要字段
CREATE TABLE Files (
    Id varchar(36) NOT NULL PRIMARY KEY,
    Name varchar(255) NOT NULL,
    OriginalName varchar(255) NOT NULL,
    FileType varchar(50) NOT NULL,
    MimeType varchar(100) NOT NULL,
    Size bigint NOT NULL,
    Extension varchar(10) NOT NULL,
    StoragePath varchar(500) NOT NULL,
    Hash varchar(64) DEFAULT NULL,
    FolderId varchar(36) NOT NULL,
    UploadedById varchar(36) NOT NULL,
    CreatedAt datetime NOT NULL,
    UpdatedAt datetime NOT NULL,
    Description varchar(500) DEFAULT NULL,
    IsWatermarked tinyint(1) NOT NULL DEFAULT 0,
    WatermarkedAt datetime DEFAULT NULL,
    Status varchar(20) NOT NULL DEFAULT 'Normal',
    Tags varchar(1000) DEFAULT NULL,
    Metadata text DEFAULT NULL,
    KEY IX_Files_FolderId (FolderId),
    KEY IX_Files_FileType (FileType),
    KEY IX_Files_Hash (Hash),
    KEY IX_Files_CreatedAt (CreatedAt),
    KEY IX_Files_UploadedById (UploadedById),
    UNIQUE KEY IX_Files_FolderId_Name (FolderId, Name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证结果
SELECT 'Files table created successfully' as Result;
DESCRIBE Files;
