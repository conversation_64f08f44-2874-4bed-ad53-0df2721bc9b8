using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WatermarkCore.Models
{
    /// <summary>
    /// 文件夹实体
    /// </summary>
    public class Folder
    {
        /// <summary>
        /// 文件夹ID
        /// </summary>
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 文件夹名称
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 父文件夹ID，null表示根文件夹
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 父文件夹导航属性
        /// </summary>
        [ForeignKey(nameof(ParentId))]
        public virtual Folder? Parent { get; set; }

        /// <summary>
        /// 子文件夹集合
        /// </summary>
        public virtual ICollection<Folder> Children { get; set; } = new List<Folder>();

        /// <summary>
        /// 文件夹中的文件集合
        /// </summary>
        public virtual ICollection<FileItem> Files { get; set; } = new List<FileItem>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 文件夹路径（从根目录开始的完整路径）
        /// </summary>
        [StringLength(1000)]
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 文件夹层级深度
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 是否为系统文件夹（如根文件夹）
        /// </summary>
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 文件夹描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }
    }
}
