<script setup>
import { ref, onMounted } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import FileManager from './components/FileManager.vue'
import WatermarkEditor from './components/WatermarkEditor.vue'
import StatusBar from './components/StatusBar.vue'
import HelpDialog from './components/HelpDialog.vue'
import { useFileStore } from './stores/fileStore'

const fileStore = useFileStore()
const selectedFile = ref(null)
const selectedFolder = ref(null)
const showHelp = ref(false)
const sidebarWidth = ref(280)
const isResizing = ref(false)
const fileManagerRef = ref(null)

// 初始化数据
onMounted(async () => {
  await fileStore.initializeData()
})

const handleFileSelected = (file) => {
  selectedFile.value = file
  selectedFolder.value = null
  fileStore.selectFile(file)
}

const handleFolderSelected = (folder) => {
  selectedFolder.value = folder
  selectedFile.value = null
}

const handleWatermarkApplied = (result) => {
  // 处理水印应用结果
  console.log('水印已应用:', result)

  // 下载文件
  const link = document.createElement('a')
  link.href = URL.createObjectURL(result.blob)
  link.download = result.filename
  link.click()

  // 清理URL对象
  URL.revokeObjectURL(link.href)
}

// 拖拽调整侧边栏宽度
const startResize = (e) => {
  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

const handleResize = (e) => {
  if (!isResizing.value) return

  const newWidth = e.clientX
  // 最小宽度设为280px，确保工具栏按钮不会被压缩
  if (newWidth >= 200 && newWidth <= 600) {
    sidebarWidth.value = newWidth
  }
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <div class="header-content">
        <div class="header-text">
          <h1>水印系统</h1>
          <p>支持图片、PDF、Word文档的水印添加</p>
        </div>
        <el-button type="primary" plain @click="showHelp = true">
          <el-icon><QuestionFilled /></el-icon>
          使用帮助
        </el-button>
      </div>
    </header>

    <main class="app-main">
      <div class="sidebar" :style="{ width: sidebarWidth + 'px' }">
        <FileManager
          @file-selected="handleFileSelected"
          @folder-selected="handleFolderSelected"
          ref="fileManagerRef"
        />
      </div>

      <div
        class="resize-handle"
        @mousedown="startResize"
      ></div>

      <div class="content">
        <WatermarkEditor
          :selected-file="selectedFile"
          :selected-folder="selectedFolder"
          @watermark-applied="handleWatermarkApplied"
        />
      </div>
    </main>

    <StatusBar :selected-file="selectedFile" />

    <HelpDialog v-model:visible="showHelp" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f5f5f5;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-text {
  text-align: left;
}

.header-text h1 {
  font-size: 2rem;
  margin-bottom: 8px;
}

.header-text p {
  opacity: 0.9;
  font-size: 1rem;
  margin: 0;
}

.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  background: white;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  min-width: 160px;
  max-width: 600px;
}

.resize-handle {
  width: 4px;
  background: #ebeef5;
  cursor: col-resize;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background: #409eff;
}

.content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

/* Element Plus 样式覆盖 */
.el-tree-node__content {
  height: 40px;
}

.el-upload-dragger {
  width: 100%;
}
</style>
