using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WatermarkCore.Models
{
    /// <summary>
    /// 文件实体
    /// </summary>
    public class FileItem
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 文件名称（包含扩展名）
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 原始文件名（用户上传时的文件名）
        /// </summary>
        [Required]
        [StringLength(255)]
        public string OriginalName { get; set; } = string.Empty;

        /// <summary>
        /// 文件类型（image, pdf, word等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// MIME类型
        /// </summary>
        [Required]
        [StringLength(100)]
        public string MimeType { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        [StringLength(10)]
        public string Extension { get; set; } = string.Empty;

        /// <summary>
        /// 文件存储路径（相对路径）
        /// </summary>
        [Required]
        [StringLength(500)]
        public string StoragePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件哈希值（用于去重和完整性校验）
        /// </summary>
        [StringLength(64)]
        public string? Hash { get; set; }

        /// <summary>
        /// 所属文件夹ID
        /// </summary>
        [Required]
        public string FolderId { get; set; } = string.Empty;

        /// <summary>
        /// 所属文件夹导航属性
        /// </summary>
        [ForeignKey(nameof(FolderId))]
        public virtual Folder Folder { get; set; } = null!;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 文件描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否已处理水印
        /// </summary>
        public bool IsWatermarked { get; set; } = false;

        /// <summary>
        /// 水印处理时间
        /// </summary>
        public DateTime? WatermarkedAt { get; set; }

        /// <summary>
        /// 文件状态（正常、已删除、处理中等）
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "Normal";

        /// <summary>
        /// 文件标签（JSON格式存储）
        /// </summary>
        [StringLength(1000)]
        public string? Tags { get; set; }

        /// <summary>
        /// 文件元数据（JSON格式存储，如图片尺寸、PDF页数等）
        /// </summary>
        [Column(TypeName = "text")]
        public string? Metadata { get; set; }
    }
}
