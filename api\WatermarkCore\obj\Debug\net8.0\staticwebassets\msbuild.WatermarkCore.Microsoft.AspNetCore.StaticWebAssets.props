﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\aad56d02-85fa-4452-a727-24299a7a7bc5_4FD208543529D44B30E586A9A66719C5.png'))">
      <SourceType>Package</SourceType>
      <SourceId>WatermarkCore</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/WatermarkCore</BasePath>
      <RelativePath>uploads/2025/08/04/aad56d02-85fa-4452-a727-24299a7a7bc5_4FD208543529D44B30E586A9A66719C5.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>t7xrxpdv6t</Fingerprint>
      <Integrity>6TVz5/53CbwzXX+bIGGsLbsBAnD4QkOv1Zd6o/0s9IE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\aad56d02-85fa-4452-a727-24299a7a7bc5_4FD208543529D44B30E586A9A66719C5.png'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>